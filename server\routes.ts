import express, { type Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertProcessedImageSchema, type WatermarkRemovalResponse } from "@shared/schema";
import { z } from "zod";
import multer from "multer";
import path from "path";
import fs from "fs";

const upload = multer({
  dest: 'uploads/',
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB
  },
  fileFilter: (req, file, cb) => {
    const allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp'];
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPG, JPEG, PNG, BMP allowed.'));
    }
  }
});

// TextIn API integration - Updated to use watermark removal endpoint
async function processWithTextInAPI(filePath: string): Promise<WatermarkRemovalResponse> {
  const startTime = Date.now();

  // Check if we're in demo mode (no valid API keys)
  const appId = process.env.TEXTIN_APP_ID || 'd116412bff6bb3272cddfcd730bc99a8';
  const secretCode = process.env.TEXTIN_SECRET_CODE || 'c1eddc385f74f8cc9a80ec3614a446c5';
  const isDemoMode = !process.env.TEXTIN_APP_ID || !process.env.TEXTIN_SECRET_CODE;

  if (isDemoMode) {
    // Demo mode: simulate processing with a delay
    console.log('Running in demo mode - simulating watermark removal...');
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000)); // 1-3 second delay

    const processingTime = Date.now() - startTime;

    // In demo mode, return the original file as the "processed" result
    // This simulates successful processing without requiring external API
    return {
      success: true,
      processedImageUrl: `/uploads/${path.basename(filePath)}`,
      processingTime
    };
  }

  try {
    const fileBuffer = fs.readFileSync(filePath);

    // Use the correct TextIn API endpoint for watermark removal
    const response = await fetch('https://api.textin.com/ai/service/v1/image_watermark_removal', {
      method: 'POST',
      headers: {
        'x-ti-app-id': appId,
        'x-ti-secret-code': secretCode,
        'Content-Type': 'application/octet-stream'
      },
      body: fileBuffer
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`TextIn API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    const processingTime = Date.now() - startTime;

    // Handle TextIn API response format
    if (result.code === 200 && result.result) {
      return {
        success: true,
        processedImageUrl: result.result.processed_image_url || result.result.image_url,
        processingTime
      };
    } else {
      throw new Error(result.message || 'Processing failed');
    }
  } catch (error) {
    console.error('TextIn API error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      processingTime: Date.now() - startTime
    };
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  
  // Upload and process watermark removal
  app.post('/api/watermark-removal', upload.single('image'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      // Create database entry
      const processedImage = await storage.createProcessedImage({
        originalFileName: req.file.originalname,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        status: 'processing',
        metadata: {
          uploadPath: req.file.path
        }
      });

      // Process with TextIn API
      const result = await processWithTextInAPI(req.file.path);
      
      // Update database with result
      await storage.updateProcessedImage(processedImage.id, {
        status: result.success ? 'completed' : 'failed',
        processingTime: result.processingTime,
        processedFileName: result.processedImageUrl,
        errorMessage: result.error
      });

      // Clean up uploaded file (except in demo mode where we serve it as the result)
      const isDemoMode = !process.env.TEXTIN_APP_ID || !process.env.TEXTIN_SECRET_CODE;
      if (!isDemoMode) {
        fs.unlinkSync(req.file.path);
      }

      res.json({
        id: processedImage.id,
        ...result
      });
    } catch (error) {
      console.error('Upload error:', error);
      res.status(500).json({ 
        error: error instanceof Error ? error.message : 'Internal server error' 
      });
    }
  });

  // Get processing status
  app.get('/api/watermark-removal/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const processedImage = await storage.getProcessedImage(id);
      
      if (!processedImage) {
        return res.status(404).json({ error: 'Image not found' });
      }

      res.json({
        id: processedImage.id,
        status: processedImage.status,
        processedImageUrl: processedImage.processedFileName,
        processingTime: processedImage.processingTime,
        error: processedImage.errorMessage
      });
    } catch (error) {
      console.error('Status check error:', error);
      res.status(500).json({ 
        error: error instanceof Error ? error.message : 'Internal server error' 
      });
    }
  });

  // Serve uploaded files (for demo mode)
  app.use('/uploads', express.static('uploads'));

  // Health check
  app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });

  const httpServer = createServer(app);
  return httpServer;
}
